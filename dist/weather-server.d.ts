/**
 * Weather MCP Server
 * Provides weather information through the Model Context Protocol
 */
export declare class WeatherMCPServer {
    private server;
    private weatherService;
    constructor(apiKey: string);
    private setupHandlers;
    private handleGetWeather;
    private handleClearCache;
    private handleGetCacheStats;
    /**
     * Start the MCP server
     */
    start(): Promise<void>;
    /**
     * Stop the MCP server
     */
    stop(): Promise<void>;
}
//# sourceMappingURL=weather-server.d.ts.map