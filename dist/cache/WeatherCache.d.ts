import { WeatherData, CacheOptions } from '../types/weather.js';
/**
 * Weather data cache implementation
 */
export declare class WeatherCache {
    private cache;
    private readonly defaultTTL;
    private readonly maxSize;
    constructor(options?: CacheOptions);
    /**
     * Get cached weather data for a location
     */
    get(location: string): WeatherData | null;
    /**
     * Set weather data in cache
     */
    set(location: string, data: WeatherData, ttl?: number): void;
    /**
     * Check if location data is cached and valid
     */
    has(location: string): boolean;
    /**
     * Clear expired entries from cache
     */
    cleanup(): void;
    /**
     * Clear all cache entries
     */
    clear(): void;
    /**
     * Get cache statistics
     */
    getStats(): {
        size: number;
        maxSize: number;
        hitRate?: number;
    };
    /**
     * Normalize location key for consistent caching
     */
    private normalizeKey;
}
//# sourceMappingURL=WeatherCache.d.ts.map