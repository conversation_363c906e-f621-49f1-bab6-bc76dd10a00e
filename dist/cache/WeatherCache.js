/**
 * Weather data cache implementation
 */
export class WeatherCache {
    cache = new Map();
    defaultTTL;
    maxSize;
    constructor(options = {}) {
        this.defaultTTL = options.ttl || 5 * 60 * 1000; // 5 minutes default
        this.maxSize = options.maxSize || 100;
    }
    /**
     * Get cached weather data for a location
     */
    get(location) {
        const key = this.normalizeKey(location);
        const entry = this.cache.get(key);
        if (!entry) {
            return null;
        }
        // Check if entry has expired
        if (Date.now() > entry.timestamp + entry.ttl) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    /**
     * Set weather data in cache
     */
    set(location, data, ttl) {
        const key = this.normalizeKey(location);
        // Remove oldest entries if cache is full
        if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
            const oldestKey = this.cache.keys().next().value;
            this.cache.delete(oldestKey);
        }
        const entry = {
            data,
            timestamp: Date.now(),
            ttl: ttl || this.defaultTTL
        };
        this.cache.set(key, entry);
    }
    /**
     * Check if location data is cached and valid
     */
    has(location) {
        return this.get(location) !== null;
    }
    /**
     * Clear expired entries from cache
     */
    cleanup() {
        const now = Date.now();
        for (const [key, entry] of this.cache.entries()) {
            if (now > entry.timestamp + entry.ttl) {
                this.cache.delete(key);
            }
        }
    }
    /**
     * Clear all cache entries
     */
    clear() {
        this.cache.clear();
    }
    /**
     * Get cache statistics
     */
    getStats() {
        return {
            size: this.cache.size,
            maxSize: this.maxSize
        };
    }
    /**
     * Normalize location key for consistent caching
     */
    normalizeKey(location) {
        return location.toLowerCase().trim();
    }
}
//# sourceMappingURL=WeatherCache.js.map