/**
 * Validation utilities for weather service
 */
export interface ValidationResult {
    isValid: boolean;
    error?: string;
}
/**
 * Validate location string
 */
export declare function validateLocation(location: string): ValidationResult;
/**
 * Validate units parameter
 */
export declare function validateUnits(units?: string): ValidationResult;
/**
 * Validate API key format
 */
export declare function validateApiKey(apiKey: string): ValidationResult;
/**
 * Sanitize location string for API requests
 */
export declare function sanitizeLocation(location: string): string;
//# sourceMappingURL=validation.d.ts.map