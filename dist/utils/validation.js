/**
 * Validation utilities for weather service
 */
/**
 * Validate location string
 */
export function validateLocation(location) {
    if (!location || typeof location !== 'string') {
        return {
            isValid: false,
            error: 'Location must be a non-empty string'
        };
    }
    const trimmed = location.trim();
    if (trimmed.length === 0) {
        return {
            isValid: false,
            error: 'Location cannot be empty'
        };
    }
    if (trimmed.length > 100) {
        return {
            isValid: false,
            error: 'Location name too long (max 100 characters)'
        };
    }
    // Basic validation for potentially malicious input
    const invalidChars = /[<>{}[\]\\]/;
    if (invalidChars.test(trimmed)) {
        return {
            isValid: false,
            error: 'Location contains invalid characters'
        };
    }
    return { isValid: true };
}
/**
 * Validate units parameter
 */
export function validateUnits(units) {
    if (!units) {
        return { isValid: true }; // Optional parameter
    }
    const validUnits = ['metric', 'imperial', 'kelvin'];
    if (!validUnits.includes(units)) {
        return {
            isValid: false,
            error: `Invalid units. Must be one of: ${validUnits.join(', ')}`
        };
    }
    return { isValid: true };
}
/**
 * Validate API key format
 */
export function validateApiKey(apiKey) {
    if (!apiKey || typeof apiKey !== 'string') {
        return {
            isValid: false,
            error: 'API key must be a non-empty string'
        };
    }
    const trimmed = apiKey.trim();
    if (trimmed.length === 0) {
        return {
            isValid: false,
            error: 'API key cannot be empty'
        };
    }
    // Basic format validation (OpenWeatherMap API keys are typically 32 characters)
    if (trimmed.length < 10 || trimmed.length > 50) {
        return {
            isValid: false,
            error: 'API key format appears invalid'
        };
    }
    return { isValid: true };
}
/**
 * Sanitize location string for API requests
 */
export function sanitizeLocation(location) {
    return location.trim().replace(/\s+/g, ' ');
}
//# sourceMappingURL=validation.js.map