/**
 * Retry utility for handling API failures
 */
/**
 * Retry a function with exponential backoff
 */
export async function retry(fn, options = {}) {
    const { maxAttempts = 3, baseDelay = 1000, maxDelay = 10000, backoffFactor = 2, retryCondition = (error) => true } = options;
    let lastError;
    let attempts = 0;
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        attempts = attempt;
        try {
            const result = await fn();
            return {
                success: true,
                data: result,
                attempts
            };
        }
        catch (error) {
            lastError = error instanceof Error ? error : new Error(String(error));
            // Don't retry if this is the last attempt or if retry condition fails
            if (attempt === maxAttempts || !retryCondition(error)) {
                break;
            }
            // Calculate delay with exponential backoff
            const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt - 1), maxDelay);
            // Add some jitter to prevent thundering herd
            const jitteredDelay = delay + Math.random() * 1000;
            await sleep(jitteredDelay);
        }
    }
    return {
        success: false,
        error: lastError,
        attempts
    };
}
/**
 * Sleep for specified milliseconds
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
/**
 * Default retry condition for HTTP errors
 */
export function shouldRetryHttpError(error) {
    // Retry on network errors or 5xx server errors
    if (error.code === 'ECONNRESET' ||
        error.code === 'ENOTFOUND' ||
        error.code === 'ECONNREFUSED') {
        return true;
    }
    // Retry on 5xx HTTP status codes
    if (error.response && error.response.status >= 500) {
        return true;
    }
    // Retry on rate limiting (429)
    if (error.response && error.response.status === 429) {
        return true;
    }
    return false;
}
//# sourceMappingURL=retry.js.map