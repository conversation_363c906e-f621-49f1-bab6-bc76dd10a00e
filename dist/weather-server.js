import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ErrorCode, ListToolsRequestSchema, McpError, } from '@modelcontextprotocol/sdk/types.js';
import { WeatherService } from './services/WeatherService.js';
/**
 * Weather MCP Server
 * Provides weather information through the Model Context Protocol
 */
export class WeatherMCPServer {
    server;
    weatherService;
    constructor(apiKey) {
        this.server = new Server({
            name: 'weather-server',
            version: '1.0.0',
        }, {
            capabilities: {
                tools: {},
            },
        });
        this.weatherService = new WeatherService(apiKey);
        this.setupHandlers();
    }
    setupHandlers() {
        // List available tools
        this.server.setRequestHandler(ListToolsRequestSchema, async () => {
            return {
                tools: [
                    {
                        name: 'get_weather',
                        description: 'Get current weather information for a location',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                location: {
                                    type: 'string',
                                    description: 'The city name or location to get weather for',
                                },
                                units: {
                                    type: 'string',
                                    enum: ['metric', 'imperial', 'kelvin'],
                                    description: 'Temperature units (default: metric)',
                                    default: 'metric',
                                },
                            },
                            required: ['location'],
                        },
                    },
                    {
                        name: 'clear_weather_cache',
                        description: 'Clear the weather data cache',
                        inputSchema: {
                            type: 'object',
                            properties: {},
                        },
                    },
                    {
                        name: 'get_cache_stats',
                        description: 'Get weather cache statistics',
                        inputSchema: {
                            type: 'object',
                            properties: {},
                        },
                    },
                ],
            };
        });
        // Handle tool calls
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;
            try {
                switch (name) {
                    case 'get_weather':
                        return await this.handleGetWeather(args);
                    case 'clear_weather_cache':
                        return this.handleClearCache();
                    case 'get_cache_stats':
                        return this.handleGetCacheStats();
                    default:
                        throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${name}`);
                }
            }
            catch (error) {
                if (error instanceof McpError) {
                    throw error;
                }
                throw new McpError(ErrorCode.InternalError, `Tool execution failed: ${error instanceof Error ? error.message : String(error)}`);
            }
        });
    }
    async handleGetWeather(request) {
        const result = await this.weatherService.getWeather(request);
        if (!result.success) {
            throw new McpError(ErrorCode.InvalidRequest, result.error || 'Failed to get weather data');
        }
        const weatherData = result.data;
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify({
                        location: weatherData.location,
                        temperature: weatherData.temperature,
                        humidity: weatherData.humidity,
                        pressure: weatherData.pressure,
                        windSpeed: weatherData.windSpeed,
                        windDirection: weatherData.windDirection,
                        description: weatherData.description,
                        timestamp: weatherData.timestamp.toISOString(),
                        cached: result.cached || false,
                    }, null, 2),
                },
            ],
        };
    }
    handleClearCache() {
        this.weatherService.clearCache();
        return {
            content: [
                {
                    type: 'text',
                    text: 'Weather cache cleared successfully',
                },
            ],
        };
    }
    handleGetCacheStats() {
        const stats = this.weatherService.getCacheStats();
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify(stats, null, 2),
                },
            ],
        };
    }
    /**
     * Start the MCP server
     */
    async start() {
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        console.error('Weather MCP server running on stdio');
    }
    /**
     * Stop the MCP server
     */
    async stop() {
        await this.server.close();
    }
}
//# sourceMappingURL=weather-server.js.map