/**
 * Weather data types and interfaces
 */
export interface WeatherData {
    location: string;
    temperature: number;
    humidity: number;
    pressure: number;
    windSpeed: number;
    windDirection: number;
    description: string;
    timestamp: Date;
}
export interface WeatherRequest {
    location: string;
    units?: 'metric' | 'imperial' | 'kelvin';
}
export interface WeatherResponse {
    success: boolean;
    data?: WeatherData;
    error?: string;
    cached?: boolean;
}
export interface WeatherApiResponse {
    coord: {
        lon: number;
        lat: number;
    };
    weather: Array<{
        id: number;
        main: string;
        description: string;
        icon: string;
    }>;
    base: string;
    main: {
        temp: number;
        feels_like: number;
        temp_min: number;
        temp_max: number;
        pressure: number;
        humidity: number;
    };
    visibility: number;
    wind: {
        speed: number;
        deg: number;
    };
    clouds: {
        all: number;
    };
    dt: number;
    sys: {
        type: number;
        id: number;
        country: string;
        sunrise: number;
        sunset: number;
    };
    timezone: number;
    id: number;
    name: string;
    cod: number;
}
export interface CacheEntry {
    data: WeatherData;
    timestamp: number;
    ttl: number;
}
export interface CacheOptions {
    ttl?: number;
    maxSize?: number;
}
//# sourceMappingURL=weather.d.ts.map