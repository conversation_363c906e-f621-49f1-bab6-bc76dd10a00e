import { WeatherRequest, WeatherResponse } from '../types/weather.js';
/**
 * Weather service for fetching weather data from OpenWeatherMap API
 */
export declare class WeatherService {
    private cache;
    private apiKey;
    private baseUrl;
    constructor(apiKey: string, cacheOptions?: {
        ttl?: number;
        maxSize?: number;
    });
    /**
     * Get weather data for a location
     */
    getWeather(request: WeatherRequest): Promise<WeatherResponse>;
    /**
     * Fetch weather data from OpenWeatherMap API
     */
    private fetchWeatherData;
    /**
     * Transform API response to internal weather data format
     */
    private transformApiResponse;
    /**
     * Clear cache
     */
    clearCache(): void;
    /**
     * Get cache statistics
     */
    getCacheStats(): {
        size: number;
        maxSize: number;
        hitRate?: number;
    };
}
//# sourceMappingURL=WeatherService.d.ts.map