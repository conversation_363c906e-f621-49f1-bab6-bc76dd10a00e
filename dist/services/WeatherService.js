import { WeatherCache } from '../cache/WeatherCache.js';
import { validateLocation, validateUnits, validateApiKey, sanitizeLocation } from '../utils/validation.js';
import { retry, shouldRetryHttpError } from '../utils/retry.js';
/**
 * Weather service for fetching weather data from OpenWeatherMap API
 */
export class WeatherService {
    cache;
    apiKey;
    baseUrl = 'https://api.openweathermap.org/data/2.5/weather';
    constructor(apiKey, cacheOptions) {
        const validation = validateApiKey(apiKey);
        if (!validation.isValid) {
            throw new Error(`Invalid API key: ${validation.error}`);
        }
        this.apiKey = apiKey;
        this.cache = new WeatherCache(cacheOptions);
    }
    /**
     * Get weather data for a location
     */
    async getWeather(request) {
        // Validate input
        const locationValidation = validateLocation(request.location);
        if (!locationValidation.isValid) {
            return {
                success: false,
                error: locationValidation.error
            };
        }
        const unitsValidation = validateUnits(request.units);
        if (!unitsValidation.isValid) {
            return {
                success: false,
                error: unitsValidation.error
            };
        }
        const location = sanitizeLocation(request.location);
        const cacheKey = `${location}_${request.units || 'metric'}`;
        // Check cache first
        const cachedData = this.cache.get(cacheKey);
        if (cachedData) {
            return {
                success: true,
                data: cachedData,
                cached: true
            };
        }
        // Fetch from API with retry logic
        try {
            const result = await retry(() => this.fetchWeatherData(location, request.units), {
                maxAttempts: 3,
                baseDelay: 1000,
                retryCondition: shouldRetryHttpError
            });
            if (!result.success) {
                return {
                    success: false,
                    error: result.error?.message || 'Failed to fetch weather data'
                };
            }
            const weatherData = this.transformApiResponse(result.data, location);
            // Cache the result
            this.cache.set(cacheKey, weatherData);
            return {
                success: true,
                data: weatherData,
                cached: false
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
    /**
     * Fetch weather data from OpenWeatherMap API
     */
    async fetchWeatherData(location, units) {
        const params = new URLSearchParams({
            q: location,
            appid: this.apiKey,
            units: units || 'metric'
        });
        const url = `${this.baseUrl}?${params.toString()}`;
        const response = await fetch(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    }
    /**
     * Transform API response to internal weather data format
     */
    transformApiResponse(apiResponse, location) {
        return {
            location: apiResponse.name || location,
            temperature: apiResponse.main.temp,
            humidity: apiResponse.main.humidity,
            pressure: apiResponse.main.pressure,
            windSpeed: apiResponse.wind?.speed || 0,
            windDirection: apiResponse.wind?.deg || 0,
            description: apiResponse.weather[0]?.description || 'No description',
            timestamp: new Date()
        };
    }
    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
    }
    /**
     * Get cache statistics
     */
    getCacheStats() {
        return this.cache.getStats();
    }
}
//# sourceMappingURL=WeatherService.js.map