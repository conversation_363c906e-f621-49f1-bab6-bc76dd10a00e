#!/usr/bin/env node
import { WeatherMCPServer } from './weather-server.js';
import { validateApi<PERSON><PERSON> } from './utils/validation.js';
/**
 * Main entry point for the Weather MCP Server
 */
async function main() {
    try {
        // Get API key from environment variable
        const apiKey = process.env.OPENWEATHER_API_KEY;
        if (!apiKey) {
            console.error('Error: OPENWEATHER_API_KEY environment variable is required');
            console.error('Please set your OpenWeatherMap API key:');
            console.error('export OPENWEATHER_API_KEY="your-api-key-here"');
            process.exit(1);
        }
        // Validate API key format
        const validation = validateApiKey(apiKey);
        if (!validation.isValid) {
            console.error(`Error: Invalid API key format - ${validation.error}`);
            process.exit(1);
        }
        // Create and start the server
        const server = new WeatherMCPServer(apiKey);
        // Handle graceful shutdown
        process.on('SIGINT', async () => {
            console.error('Received SIGINT, shutting down gracefully...');
            await server.stop();
            process.exit(0);
        });
        process.on('SIGTERM', async () => {
            console.error('Received SIGTERM, shutting down gracefully...');
            await server.stop();
            process.exit(0);
        });
        // Start the server
        await server.start();
    }
    catch (error) {
        console.error('Failed to start Weather MCP Server:', error);
        process.exit(1);
    }
}
// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
// Run the main function
main().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
});
//# sourceMappingURL=main.js.map