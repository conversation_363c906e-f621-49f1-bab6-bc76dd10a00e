#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/weather-mcp-server-ts/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.28.0_@jest+transform@29.7.0_@jest+types@29.6.3_babel-jest@29.7.0_7cm22f3evicqxyxgd6dwxf7rsa/node_modules/ts-jest/node_modules:/Users/<USER>/Desktop/weather-mcp-server-ts/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.28.0_@jest+transform@29.7.0_@jest+types@29.6.3_babel-jest@29.7.0_7cm22f3evicqxyxgd6dwxf7rsa/node_modules:/Users/<USER>/Desktop/weather-mcp-server-ts/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/weather-mcp-server-ts/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.28.0_@jest+transform@29.7.0_@jest+types@29.6.3_babel-jest@29.7.0_7cm22f3evicqxyxgd6dwxf7rsa/node_modules/ts-jest/node_modules:/Users/<USER>/Desktop/weather-mcp-server-ts/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.28.0_@jest+transform@29.7.0_@jest+types@29.6.3_babel-jest@29.7.0_7cm22f3evicqxyxgd6dwxf7rsa/node_modules:/Users/<USER>/Desktop/weather-mcp-server-ts/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-jest/cli.js" "$@"
else
  exec node  "$basedir/../ts-jest/cli.js" "$@"
fi
